# The Flutter tooling requires that developers have CMake 3.10 or later
# installed. You should not increase this version, as doing so will cause
# the plugin to fail to compile for some customers of the plugin.
cmake_minimum_required(VERSION 3.10)

project({{projectName}}_library VERSION 0.0.1 LANGUAGES C)

add_library({{projectName}} SHARED
  "{{projectName}}.c"
)

set_target_properties({{projectName}} PROPERTIES
  PUBLIC_HEADER {{projectName}}.h
  OUTPUT_NAME "{{projectName}}"
)

target_compile_definitions({{projectName}} PUBLIC DART_SHARED_LIB)
