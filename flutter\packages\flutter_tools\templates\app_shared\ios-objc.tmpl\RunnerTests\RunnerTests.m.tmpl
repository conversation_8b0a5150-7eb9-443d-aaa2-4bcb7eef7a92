#import <Flutter/Flutter.h>
#import <UIKit/UIKit.h>
#import <XCTest/XCTest.h>
{{#withPlatformChannelPluginHook}}

{{#withSwiftPackageManager}}
// If your plugin has been explicitly set to "type: .dynamic" in the Package.swift,
// you will need to add your plugin as a dependency of RunnerTests within Xcode.
{{/withSwiftPackageManager}}

@import {{pluginProjectName}};

// This demonstrates a simple unit test of the Objective-C portion of this plugin's implementation.
//
// See https://developer.apple.com/documentation/xctest for more information about using XCTest.
{{/withPlatformChannelPluginHook}}

@interface RunnerTests : XCTestCase

@end

@implementation RunnerTests

{{#withPlatformChannelPluginHook}}
- (void)testExample {
  {{pluginClass}} *plugin = [[{{pluginClass}} alloc] init];

  FlutterMethodCall *call = [FlutterMethodCall methodCallWithMethodName:@"getPlatformVersion"
                                                              arguments:nil];
  XCTestExpectation *expectation = [self expectationWithDescription:@"result block must be called"];
  [plugin handleMethodCall:call
                    result:^(id result) {
                      NSString *expected = [NSString
                          stringWithFormat:@"iOS %@", UIDevice.currentDevice.systemVersion];
                      XCTAssertEqualObjects(result, expected);
                      [expectation fulfill];
                    }];
  [self waitForExpectationsWithTimeout:1 handler:nil];
}
{{/withPlatformChannelPluginHook}}
{{^withPlatformChannelPluginHook}}
- (void)testExample {
  // If you add code to the Runner application, consider adding tests here.
  // See https://developer.apple.com/documentation/xctest for more information about using XCTest.
}
{{/withPlatformChannelPluginHook}}

@end
