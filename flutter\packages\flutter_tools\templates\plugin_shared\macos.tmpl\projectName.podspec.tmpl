#
# To learn more about a Podspec see http://guides.cocoapods.org/syntax/podspec.html.
# Run `pod lib lint {{projectName}}.podspec` to validate before publishing.
#
Pod::Spec.new do |s|
  s.name             = '{{projectName}}'
  s.version          = '0.0.1'
  s.summary          = '{{description}}'
  s.description      = <<-DESC
{{description}}
                       DESC
  s.homepage         = 'http://example.com'
  s.license          = { :file => '../LICENSE' }
  s.author           = { 'Your Company' => '<EMAIL>' }

{{#withFfiPluginHook}}
  # This will ensure the source files in Classes/ are included in the native
  # builds of apps using this FFI plugin. Podspec does not support relative
  # paths, so Classes contains a forwarder C file that relatively imports
  # `../src/*` so that the C sources can be shared among all target platforms.
{{/withFfiPluginHook}}
  s.source           = { :path => '.' }
  {{#withSwiftPackageManager}}
  s.source_files = '{{projectName}}/Sources/{{projectName}}/**/*'
  {{/withSwiftPackageManager}}
  {{^withSwiftPackageManager}}
  s.source_files = 'Classes/**/*'
  {{/withSwiftPackageManager}}
  s.dependency 'FlutterMacOS'

  s.platform = :osx, '10.11'
  s.pod_target_xcconfig = { 'DEFINES_MODULE' => 'YES' }
  s.swift_version = '5.0'
end
