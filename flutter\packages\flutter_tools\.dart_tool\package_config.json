{"configVersion": 2, "packages": [{"name": "_fe_analyzer_shared", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/_fe_analyzer_shared-72.0.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "_macros", "rootUri": "file:///C:/Users/<USER>/Desktop/T/flutter/bin/cache/dart-sdk/pkg/_macros", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "analyzer", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/analyzer-6.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "archive", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/archive-3.6.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "args", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/args-2.5.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "async", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/async-2.11.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "boolean_selector", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/boolean_selector-2.1.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "browser_launcher", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/browser_launcher-1.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "built_collection", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/built_collection-5.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "built_value", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/built_value-8.9.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "checked_yaml", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/checked_yaml-2.0.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "cli_config", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/cli_config-0.2.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "clock", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/clock-1.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "collection", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/collection-1.18.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "completion", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/completion-1.0.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "convert", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/convert-3.1.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "coverage", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/coverage-1.8.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "crypto", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/crypto-3.0.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "csslib", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/csslib-1.0.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "dap", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dap-1.3.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "dds", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dds-4.2.4+2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "dds_service_extensions", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dds_service_extensions-2.0.0", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "devtools_shared", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/devtools_shared-10.0.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "dtd", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dtd-2.2.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "dwds", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dwds-24.0.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "extension_discovery", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/extension_discovery-2.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "fake_async", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/fake_async-1.3.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "ffi", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/ffi-2.1.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/file-7.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_testing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/file_testing-3.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "fixnum", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/fixnum-1.1.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "flutter_template_images", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flutter_template_images-4.2.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "frontend_server_client", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/frontend_server_client-4.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "glob", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/glob-2.1.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "graphs", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/graphs-2.3.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "html", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/html-0.15.4", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "http", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/http-0.13.6", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "http_multi_server", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/http_multi_server-3.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "http_parser", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/http_parser-4.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "intl", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/intl-0.19.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "io", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/io-1.0.4", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "js", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/js-0.7.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "json_annotation", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/json_annotation-4.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "json_rpc_2", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/json_rpc_2-3.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "logging", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/logging-1.2.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "macros", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/macros-0.1.2-main.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "matcher", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/matcher-0.12.16+1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "meta", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/meta-1.15.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "mime", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/mime-1.0.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "multicast_dns", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/multicast_dns-0.3.2+7", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "mustache_template", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/mustache_template-2.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "native_assets_builder", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/native_assets_builder-0.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "native_assets_cli", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/native_assets_cli-0.6.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "native_stack_traces", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/native_stack_traces-0.5.7", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "node_preamble", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/node_preamble-2.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "package_config", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/package_config-2.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "path", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path-1.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "petitparser", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/petitparser-6.0.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "platform", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/platform-3.1.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "pool", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pool-1.5.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "process", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/process-5.0.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "pub_semver", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pub_semver-2.1.4", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "pubspec_parse", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pubspec_parse-1.3.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "shelf", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shelf-1.4.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "shelf_packages_handler", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shelf_packages_handler-3.0.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "shelf_proxy", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shelf_proxy-1.0.4", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "shelf_static", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shelf_static-1.1.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "shelf_web_socket", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shelf_web_socket-1.0.4", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "source_map_stack_trace", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/source_map_stack_trace-2.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "source_maps", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/source_maps-0.10.12", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "source_span", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/source_span-1.10.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "sse", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/sse-4.1.5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "stack_trace", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/stack_trace-1.11.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "standard_message_codec", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/standard_message_codec-0.0.1+4", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "stream_channel", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/stream_channel-2.1.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "string_scanner", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/string_scanner-1.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "sync_http", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/sync_http-0.3.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "term_glyph", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/term_glyph-1.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "test", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/test-1.25.7", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "test_api", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/test_api-0.7.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "test_core", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/test_core-0.6.4", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "typed_data", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/typed_data-1.3.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "unified_analytics", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/unified_analytics-6.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "usage", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/usage-4.1.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "uuid", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/uuid-3.0.7", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "vm_service", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/vm_service-14.2.5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "vm_service_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/vm_service_interface-1.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "vm_snapshot_analysis", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/vm_snapshot_analysis-0.7.6", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "watcher", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/watcher-1.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/web-0.5.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "web_socket_channel", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/web_socket_channel-2.4.5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "webdriver", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/webdriver-3.0.3", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "webkit_inspection_protocol", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/webkit_inspection_protocol-1.2.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "xml", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/xml-6.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "yaml", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/yaml-3.1.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "yaml_edit", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/yaml_edit-2.2.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_tools", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.2"}], "generated": "2025-07-29T12:20:13.600865Z", "generator": "pub", "generatorVersion": "3.5.4", "flutterRoot": "file:///C:/Users/<USER>/Desktop/T/flutter", "flutterVersion": "3.24.5", "pubCache": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache"}