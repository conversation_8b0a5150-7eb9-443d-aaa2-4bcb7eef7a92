// Generated file. Do not edit.

buildscript {
    ext.kotlin_version = "{{kotlinVersion}}"
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath("com.android.tools.build:gradle:{{agpVersionForModule}}")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version")
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: "com.android.library"
apply plugin: "kotlin-android"

android {
    // Conditional for compatibility with AGP <4.2.
    if (project.android.hasProperty("namespace")) {
        namespace = "{{androidIdentifier}}"
    }

    compileSdk = {{compileSdkVersion}}
    defaultConfig {
        minSdk = {{minSdkVersion}}
        targetSdk = {{targetSdkVersion}}
    }
}

dependencies {
    implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version")
}
