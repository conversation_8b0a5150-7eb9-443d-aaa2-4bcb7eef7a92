{"version": 1.0, "_comment": "A listing of all possible template output files. Files ending in .img.tmpl correspond to files checked in to the flutter_template_images package in the flutter/packages repo, located at the same path, excluding the .img.tmpl suffix.", "files": ["templates/app/lib/main.dart.tmpl", "templates/app/pubspec.yaml.tmpl", "templates/app/README.md.tmpl", "templates/app_shared/.gitignore.tmpl", "templates/app_shared/.idea/libraries/Dart_SDK.xml.tmpl", "templates/app_shared/.idea/libraries/KotlinJavaRuntime.xml.tmpl", "templates/app_shared/.idea/modules.xml.tmpl", "templates/app_shared/.idea/runConfigurations/main_dart.xml.tmpl", "templates/app_shared/.idea/workspace.xml.tmpl", "templates/app_shared/.metadata.tmpl", "templates/app_shared/analysis_options.yaml.tmpl", "templates/app_shared/android-java.tmpl/app/build.gradle.tmpl", "templates/app_shared/android-java.tmpl/app/src/main/java/androidIdentifier/MainActivity.java.tmpl", "templates/app_shared/android-java.tmpl/build.gradle.tmpl", "templates/app_shared/android-java.tmpl/projectName_android.iml.tmpl", "templates/app_shared/android-kotlin.tmpl/app/build.gradle.tmpl", "templates/app_shared/android-kotlin.tmpl/app/src/main/kotlin/androidIdentifier/MainActivity.kt.tmpl", "templates/app_shared/android-kotlin.tmpl/build.gradle.tmpl", "templates/app_shared/android-kotlin.tmpl/projectName_android.iml.tmpl", "templates/app_shared/android.tmpl/.gitignore", "templates/app_shared/android.tmpl/app/src/debug/AndroidManifest.xml.tmpl", "templates/app_shared/android.tmpl/app/src/main/AndroidManifest.xml.tmpl", "templates/app_shared/android.tmpl/app/src/main/res/drawable-v21/launch_background.xml", "templates/app_shared/android.tmpl/app/src/main/res/drawable/launch_background.xml", "templates/app_shared/android.tmpl/app/src/main/res/mipmap-hdpi/ic_launcher.png", "templates/app_shared/android.tmpl/app/src/main/res/mipmap-mdpi/ic_launcher.png", "templates/app_shared/android.tmpl/app/src/main/res/mipmap-xhdpi/ic_launcher.png", "templates/app_shared/android.tmpl/app/src/main/res/mipmap-xxhdpi/ic_launcher.png", "templates/app_shared/android.tmpl/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png", "templates/app_shared/android.tmpl/app/src/main/res/values-night/styles.xml", "templates/app_shared/android.tmpl/app/src/main/res/values/styles.xml", "templates/app_shared/android.tmpl/app/src/profile/AndroidManifest.xml.tmpl", "templates/app_shared/android.tmpl/gradle.properties.tmpl", "templates/app_shared/android.tmpl/settings.gradle.tmpl", "templates/app_shared/android.tmpl/gradle/wrapper/gradle-wrapper.properties.tmpl", "templates/app_shared/android.tmpl/settings.gradle", "templates/app_shared/ios-objc.tmpl/Runner.xcodeproj/project.pbxproj.tmpl", "templates/app_shared/ios-objc.tmpl/Runner.xcodeproj/xcshareddata/xcschemes/Runner.xcscheme.tmpl", "templates/app_shared/ios-objc.tmpl/Runner/AppDelegate.h", "templates/app_shared/ios-objc.tmpl/Runner/AppDelegate.m", "templates/app_shared/ios-objc.tmpl/Runner/main.m", "templates/app_shared/ios-objc.tmpl/RunnerTests/RunnerTests.m.tmpl", "templates/app_shared/ios-swift.tmpl/Runner.xcodeproj/project.pbxproj.tmpl", "templates/app_shared/ios-swift.tmpl/Runner.xcodeproj/xcshareddata/xcschemes/Runner.xcscheme.tmpl", "templates/app_shared/ios-swift.tmpl/Runner/AppDelegate.swift", "templates/app_shared/ios-swift.tmpl/Runner/Runner-Bridging-Header.h", "templates/app_shared/ios-swift.tmpl/RunnerTests/RunnerTests.swift.tmpl", "templates/app_shared/ios.tmpl/.gitignore", "templates/app_shared/ios.tmpl/Flutter/AppFrameworkInfo.plist", "templates/app_shared/ios.tmpl/Flutter/Debug.xcconfig", "templates/app_shared/ios.tmpl/Flutter/Release.xcconfig", "templates/app_shared/ios.tmpl/Runner.xcodeproj/project.xcworkspace/contents.xcworkspacedata", "templates/app_shared/ios.tmpl/Runner.xcodeproj/project.xcworkspace/xcshareddata/IDEWorkspaceChecks.plist", "templates/app_shared/ios.tmpl/Runner.xcodeproj/project.xcworkspace/xcshareddata/WorkspaceSettings.xcsettings", "templates/app_shared/ios.tmpl/Runner.xcworkspace/contents.xcworkspacedata", "templates/app_shared/ios.tmpl/Runner.xcworkspace/xcshareddata/IDEWorkspaceChecks.plist", "templates/app_shared/ios.tmpl/Runner.xcworkspace/xcshareddata/WorkspaceSettings.xcsettings", "templates/app_shared/ios.tmpl/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json", "templates/app_shared/ios.tmpl/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/app_shared/ios.tmpl/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/app_shared/ios.tmpl/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/app_shared/ios.tmpl/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/app_shared/ios.tmpl/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/app_shared/ios.tmpl/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/app_shared/ios.tmpl/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/app_shared/ios.tmpl/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/app_shared/ios.tmpl/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/app_shared/ios.tmpl/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/app_shared/ios.tmpl/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/app_shared/ios.tmpl/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/app_shared/ios.tmpl/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/app_shared/ios.tmpl/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/app_shared/ios.tmpl/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/app_shared/ios.tmpl/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json", "templates/app_shared/ios.tmpl/Runner/Assets.xcassets/LaunchImage.imageset/LaunchImage.png.img.tmpl", "templates/app_shared/ios.tmpl/Runner/Assets.xcassets/LaunchImage.imageset/<EMAIL>", "templates/app_shared/ios.tmpl/Runner/Assets.xcassets/LaunchImage.imageset/<EMAIL>", "templates/app_shared/ios.tmpl/Runner/Assets.xcassets/LaunchImage.imageset/README.md", "templates/app_shared/ios.tmpl/Runner/Base.lproj/LaunchScreen.storyboard", "templates/app_shared/ios.tmpl/Runner/Base.lproj/Main.storyboard", "templates/app_shared/ios.tmpl/Runner/Info.plist.tmpl", "templates/app_shared/linux.tmpl/.gitignore", "templates/app_shared/linux.tmpl/CMakeLists.txt.tmpl", "templates/app_shared/linux.tmpl/flutter/CMakeLists.txt", "templates/app_shared/linux.tmpl/main.cc", "templates/app_shared/linux.tmpl/my_application.cc", "templates/app_shared/linux.tmpl/my_application.cc.tmpl", "templates/app_shared/linux.tmpl/my_application.h", "templates/app_shared/macos.tmpl/.gitignore", "templates/app_shared/macos.tmpl/Flutter/Flutter-Debug.xcconfig", "templates/app_shared/macos.tmpl/Flutter/Flutter-Release.xcconfig", "templates/app_shared/macos.tmpl/Runner.xcodeproj/project.pbxproj.tmpl", "templates/app_shared/macos.tmpl/Runner.xcodeproj/project.xcworkspace/xcshareddata/IDEWorkspaceChecks.plist", "templates/app_shared/macos.tmpl/Runner.xcodeproj/xcshareddata/xcschemes/Runner.xcscheme.tmpl", "templates/app_shared/macos.tmpl/Runner.xcworkspace/contents.xcworkspacedata", "templates/app_shared/macos.tmpl/Runner.xcworkspace/xcshareddata/IDEWorkspaceChecks.plist", "templates/app_shared/macos.tmpl/Runner/AppDelegate.swift", "templates/app_shared/macos.tmpl/Runner/Assets.xcassets/AppIcon.appiconset/app_icon_1024.png.img.tmpl", "templates/app_shared/macos.tmpl/Runner/Assets.xcassets/AppIcon.appiconset/app_icon_128.png.img.tmpl", "templates/app_shared/macos.tmpl/Runner/Assets.xcassets/AppIcon.appiconset/app_icon_16.png.img.tmpl", "templates/app_shared/macos.tmpl/Runner/Assets.xcassets/AppIcon.appiconset/app_icon_256.png.img.tmpl", "templates/app_shared/macos.tmpl/Runner/Assets.xcassets/AppIcon.appiconset/app_icon_32.png.img.tmpl", "templates/app_shared/macos.tmpl/Runner/Assets.xcassets/AppIcon.appiconset/app_icon_512.png.img.tmpl", "templates/app_shared/macos.tmpl/Runner/Assets.xcassets/AppIcon.appiconset/app_icon_64.png.img.tmpl", "templates/app_shared/macos.tmpl/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json", "templates/app_shared/macos.tmpl/Runner/Base.lproj/MainMenu.xib", "templates/app_shared/macos.tmpl/Runner/Configs/AppInfo.xcconfig.tmpl", "templates/app_shared/macos.tmpl/Runner/Configs/Debug.xcconfig", "templates/app_shared/macos.tmpl/Runner/Configs/Release.xcconfig", "templates/app_shared/macos.tmpl/Runner/Configs/Warnings.xcconfig", "templates/app_shared/macos.tmpl/Runner/DebugProfile.entitlements", "templates/app_shared/macos.tmpl/Runner/Info.plist", "templates/app_shared/macos.tmpl/Runner/MainFlutterWindow.swift", "templates/app_shared/macos.tmpl/Runner/Release.entitlements", "templates/app_shared/macos.tmpl/RunnerTests/RunnerTests.swift.tmpl", "templates/app_shared/projectName.iml.tmpl", "templates/app_shared/web/favicon.png.copy.tmpl", "templates/app_shared/web/icons/Icon-192.png.copy.tmpl", "templates/app_shared/web/icons/Icon-512.png.copy.tmpl", "templates/app_shared/web/icons/Icon-maskable-192.png.img.tmpl", "templates/app_shared/web/icons/Icon-maskable-512.png.img.tmpl", "templates/app_shared/web/index.html.tmpl", "templates/app_shared/web/manifest.json.tmpl", "templates/app_shared/windows.tmpl/.gitignore", "templates/app_shared/windows.tmpl/CMakeLists.txt.tmpl", "templates/app_shared/windows.tmpl/flutter/CMakeLists.txt", "templates/app_shared/windows.tmpl/runner/CMakeLists.txt", "templates/app_shared/windows.tmpl/runner/flutter_window.cpp", "templates/app_shared/windows.tmpl/runner/flutter_window.h", "templates/app_shared/windows.tmpl/runner/main.cpp.tmpl", "templates/app_shared/windows.tmpl/runner/resource.h", "templates/app_shared/windows.tmpl/runner/resources/app_icon.ico.img.tmpl", "templates/app_shared/windows.tmpl/runner/runner.exe.manifest", "templates/app_shared/windows.tmpl/runner/Runner.rc.tmpl", "templates/app_shared/windows.tmpl/runner/utils.cpp", "templates/app_shared/windows.tmpl/runner/utils.h", "templates/app_shared/windows.tmpl/runner/win32_window.cpp", "templates/app_shared/windows.tmpl/runner/win32_window.h", "templates/app_test_widget/test/widget_test.dart.tmpl", "templates/app_integration_test/integration_test/plugin_integration_test.dart.tmpl", "templates/cocoapods/Podfile-ios-objc", "templates/cocoapods/Podfile-ios-swift", "templates/cocoapods/Podfile-macos", "templates/module/android/deferred_component/build.gradle.tmpl", "templates/module/android/deferred_component/src/main/AndroidManifest.xml.tmpl", "templates/module/android/gradle/build.gradle.tmpl", "templates/module/android/gradle/gradle.properties.tmpl", "templates/module/android/gradle/settings.gradle.tmpl", "templates/module/android/gradle/src/main/AndroidManifest.xml.tmpl", "templates/module/android/host_app_common/app.tmpl/build.gradle.tmpl", "templates/module/android/host_app_common/app.tmpl/src/main/AndroidManifest.xml.tmpl", "templates/module/android/host_app_common/app.tmpl/src/main/java/androidIdentifier/host/MainActivity.java.tmpl", "templates/module/android/host_app_common/app.tmpl/src/main/res/drawable/launch_background.xml", "templates/module/android/host_app_common/app.tmpl/src/main/res/mipmap-hdpi/ic_launcher.png", "templates/module/android/host_app_common/app.tmpl/src/main/res/values/styles.xml", "templates/module/android/host_app_editable/settings.gradle.copy.tmpl", "templates/module/android/host_app_ephemeral/settings.gradle.copy.tmpl", "templates/module/android/library/Flutter.tmpl/build.gradle.tmpl", "templates/module/android/library/Flutter.tmpl/flutter.iml.copy.tmpl", "templates/module/android/library/Flutter.tmpl/src/main/AndroidManifest.xml.tmpl", "templates/module/android/library/Flutter.tmpl/src/main/java/io/flutter/facade/Flutter.java.tmpl", "templates/module/android/library/Flutter.tmpl/src/main/java/io/flutter/facade/FlutterFragment.java.tmpl", "templates/module/android/library/include_flutter.groovy.copy.tmpl", "templates/module/android/library/settings.gradle.copy.tmpl", "templates/module/android/library_new_embedding/Flutter.tmpl/build.gradle.tmpl", "templates/module/android/library_new_embedding/Flutter.tmpl/flutter.iml.copy.tmpl", "templates/module/android/library_new_embedding/Flutter.tmpl/src/main/AndroidManifest.xml.tmpl", "templates/module/android/library_new_embedding/include_flutter.groovy.copy.tmpl", "templates/module/android/library_new_embedding/settings.gradle.copy.tmpl", "templates/module/common/.gitignore.tmpl", "templates/module/common/.idea/libraries/Dart_SDK.xml.tmpl", "templates/module/common/.idea/modules.xml.tmpl", "templates/module/common/.idea/workspace.xml.tmpl", "templates/module/common/.metadata.tmpl", "templates/module/common/analysis_options.yaml.tmpl", "templates/module/common/lib/main.dart.tmpl", "templates/module/common/projectName.iml.tmpl", "templates/module/common/projectName_android.iml.tmpl", "templates/module/common/pubspec.yaml.tmpl", "templates/module/common/README.md.tmpl", "templates/module/common/test/widget_test.dart.tmpl", "templates/module/ios/host_app_ephemeral/Config.tmpl/Debug.xcconfig", "templates/module/ios/host_app_ephemeral/Config.tmpl/Flutter.xcconfig", "templates/module/ios/host_app_ephemeral/Config.tmpl/Release.xcconfig", "templates/module/ios/host_app_ephemeral/Runner.tmpl/AppDelegate.h", "templates/module/ios/host_app_ephemeral/Runner.tmpl/AppDelegate.m", "templates/module/ios/host_app_ephemeral/Runner.tmpl/Assets.xcassets/AppIcon.appiconset/Contents.json", "templates/module/ios/host_app_ephemeral/Runner.tmpl/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/module/ios/host_app_ephemeral/Runner.tmpl/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/module/ios/host_app_ephemeral/Runner.tmpl/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/module/ios/host_app_ephemeral/Runner.tmpl/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/module/ios/host_app_ephemeral/Runner.tmpl/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/module/ios/host_app_ephemeral/Runner.tmpl/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/module/ios/host_app_ephemeral/Runner.tmpl/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/module/ios/host_app_ephemeral/Runner.tmpl/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/module/ios/host_app_ephemeral/Runner.tmpl/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/module/ios/host_app_ephemeral/Runner.tmpl/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/module/ios/host_app_ephemeral/Runner.tmpl/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/module/ios/host_app_ephemeral/Runner.tmpl/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/module/ios/host_app_ephemeral/Runner.tmpl/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/module/ios/host_app_ephemeral/Runner.tmpl/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/module/ios/host_app_ephemeral/Runner.tmpl/Assets.xcassets/AppIcon.appiconset/<EMAIL>", "templates/module/ios/host_app_ephemeral/Runner.tmpl/Assets.xcassets/LaunchImage.imageset/Contents.json", "templates/module/ios/host_app_ephemeral/Runner.tmpl/Assets.xcassets/LaunchImage.imageset/LaunchImage.png", "templates/module/ios/host_app_ephemeral/Runner.tmpl/Assets.xcassets/LaunchImage.imageset/<EMAIL>", "templates/module/ios/host_app_ephemeral/Runner.tmpl/Assets.xcassets/LaunchImage.imageset/<EMAIL>", "templates/module/ios/host_app_ephemeral/Runner.tmpl/Assets.xcassets/LaunchImage.imageset/README.md", "templates/module/ios/host_app_ephemeral/Runner.tmpl/Base.lproj/LaunchScreen.storyboard", "templates/module/ios/host_app_ephemeral/Runner.tmpl/Base.lproj/Main.storyboard", "templates/module/ios/host_app_ephemeral/Runner.tmpl/Info.plist.tmpl", "templates/module/ios/host_app_ephemeral/Runner.tmpl/main.m", "templates/module/ios/host_app_ephemeral/Runner.xcodeproj.tmpl/project.pbxproj.tmpl", "templates/module/ios/host_app_ephemeral/Runner.xcodeproj.tmpl/project.xcworkspace/contents.xcworkspacedata", "templates/module/ios/host_app_ephemeral/Runner.xcodeproj.tmpl/project.xcworkspace/xcshareddata/IDEWorkspaceChecks.plist", "templates/module/ios/host_app_ephemeral/Runner.xcodeproj.tmpl/project.xcworkspace/xcshareddata/WorkspaceSettings.xcsettings", "templates/module/ios/host_app_ephemeral/Runner.xcodeproj.tmpl/xcshareddata/xcschemes/Runner.xcscheme", "templates/module/ios/host_app_ephemeral/Runner.xcworkspace.tmpl/contents.xcworkspacedata", "templates/module/ios/host_app_ephemeral/Runner.xcworkspace.tmpl/xcshareddata/IDEWorkspaceChecks.plist", "templates/module/ios/host_app_ephemeral/Runner.xcworkspace.tmpl/xcshareddata/WorkspaceSettings.xcsettings", "templates/module/ios/host_app_ephemeral_cocoapods/Config.tmpl/Debug.xcconfig", "templates/module/ios/host_app_ephemeral_cocoapods/Config.tmpl/Release.xcconfig", "templates/module/ios/host_app_ephemeral_cocoapods/Podfile.copy.tmpl", "templates/module/ios/host_app_ephemeral_cocoapods/Runner.tmpl/AppDelegate.m", "templates/module/ios/library/Flutter.tmpl/AppFrameworkInfo.plist", "templates/module/ios/library/Flutter.tmpl/podhelper.rb.tmpl", "templates/module/ios/library/Flutter.tmpl/README.md", "templates/module/README.md", "templates/package/.gitignore.tmpl", "templates/package/.idea/libraries/Dart_SDK.xml.tmpl", "templates/package/.idea/modules.xml.tmpl", "templates/package/.idea/workspace.xml.tmpl", "templates/package/.metadata.tmpl", "templates/package/analysis_options.yaml.tmpl", "templates/package/CHANGELOG.md.tmpl", "templates/package/lib/projectName.dart.tmpl", "templates/package/LICENSE.tmpl", "templates/package/projectName.iml.tmpl", "templates/package/pubspec.yaml.tmpl", "templates/package/README.md.tmpl", "templates/package/test/projectName_test.dart.tmpl", "templates/package_ffi/.gitignore.tmpl", "templates/package_ffi/.metadata.tmpl", "templates/package_ffi/analysis_options.yaml.tmpl", "templates/package_ffi/CHANGELOG.md.tmpl", "templates/package_ffi/ffigen.yaml.tmpl", "templates/package_ffi/hook/build.dart.tmpl", "templates/package_ffi/lib/projectName_bindings_generated.dart.tmpl", "templates/package_ffi/lib/projectName.dart.tmpl", "templates/package_ffi/LICENSE.tmpl", "templates/package_ffi/pubspec.yaml.tmpl", "templates/package_ffi/README.md.tmpl", "templates/package_ffi/src.tmpl/projectName.c.tmpl", "templates/package_ffi/src.tmpl/projectName.h.tmpl", "templates/package_ffi/test/projectName_test.dart.tmpl", "templates/plugin/android-java.tmpl/build.gradle.tmpl", "templates/plugin/android-java.tmpl/projectName_android.iml.tmpl", "templates/plugin/android-java.tmpl/src/main/java/androidIdentifier/pluginClass.java.tmpl", "templates/plugin/android-java.tmpl/src/test/java/androidIdentifier/pluginClassTest.java.tmpl", "templates/plugin/android-kotlin.tmpl/build.gradle.tmpl", "templates/plugin/android-kotlin.tmpl/projectName_android.iml.tmpl", "templates/plugin/android-kotlin.tmpl/src/main/kotlin/androidIdentifier/pluginClass.kt.tmpl", "templates/plugin/android-kotlin.tmpl/src/test/kotlin/androidIdentifier/pluginClassTest.kt.tmpl", "templates/plugin/android.tmpl/.gitignore", "templates/plugin/android.tmpl/gradle/wrapper/gradle-wrapper.properties", "templates/plugin/android.tmpl/gradle.properties.tmpl", "templates/plugin/android.tmpl/settings.gradle.tmpl", "templates/plugin/android.tmpl/src/main/AndroidManifest.xml.tmpl", "templates/plugin/ios-objc.tmpl/projectName.podspec.tmpl", "templates/plugin/ios-swift.tmpl/projectName.podspec.tmpl", "templates/plugin/ios.tmpl/.gitignore", "templates/plugin/lib/projectName.dart.tmpl", "templates/plugin/lib/projectName_platform_interface.dart.tmpl", "templates/plugin/lib/projectName_method_channel.dart.tmpl", "templates/plugin/linux.tmpl/CMakeLists.txt.tmpl", "templates/plugin/linux.tmpl/include/projectName.tmpl/pluginClassSnakeCase.h.tmpl", "templates/plugin/linux.tmpl/pluginClassSnakeCase.cc.tmpl", "templates/plugin/linux.tmpl/pluginClassSnakeCase_private.h.tmpl", "templates/plugin/linux.tmpl/test/pluginClassSnakeCase_test.cc.tmpl", "templates/plugin/README.md.tmpl", "templates/plugin/test/projectName_test.dart.tmpl", "templates/plugin/test/projectName_method_channel_test.dart.tmpl", "templates/plugin/windows.tmpl/CMakeLists.txt.tmpl", "templates/plugin/windows.tmpl/include/projectName.tmpl/pluginClassSnakeCase_c_api.h.tmpl", "templates/plugin/windows.tmpl/test/pluginClassSnakeCase_test.cpp.tmpl", "templates/plugin/windows.tmpl/pluginClassSnakeCase.cpp.tmpl", "templates/plugin/windows.tmpl/pluginClassSnakeCase.h.tmpl", "templates/plugin/windows.tmpl/pluginClassSnakeCase_c_api.cpp.tmpl", "templates/plugin/lib/projectName_web.dart.tmpl", "templates/plugin_ffi/android.tmpl/build.gradle.tmpl", "templates/plugin_ffi/android.tmpl/projectName_android.iml.tmpl", "templates/plugin_ffi/ffigen.yaml.tmpl", "templates/plugin_ffi/ios.tmpl/.gitignore", "templates/plugin_ffi/ios.tmpl/Classes/projectName.c.tmpl", "templates/plugin_ffi/ios.tmpl/projectName.podspec.tmpl", "templates/plugin_ffi/lib/projectName_bindings_generated.dart.tmpl", "templates/plugin_ffi/lib/projectName.dart.tmpl", "templates/plugin_ffi/linux.tmpl/CMakeLists.txt.tmpl", "templates/plugin_ffi/linux.tmpl/include/projectName.tmpl/plugin_ffiClassSnakeCase.h.tmpl", "templates/plugin_ffi/macos.tmpl/Classes/projectName.c.tmpl", "templates/plugin_ffi/README.md.tmpl", "templates/plugin_ffi/src.tmpl/CMakeLists.txt.tmpl", "templates/plugin_ffi/src.tmpl/projectName.c.tmpl", "templates/plugin_ffi/src.tmpl/projectName.h.tmpl", "templates/plugin_ffi/windows.tmpl/CMakeLists.txt.tmpl", "templates/plugin_shared/.gitignore.tmpl", "templates/plugin_shared/.idea/libraries/Dart_SDK.xml.tmpl", "templates/plugin_shared/.idea/modules.xml.tmpl", "templates/plugin_shared/.idea/runConfigurations/example_lib_main_dart.xml.tmpl", "templates/plugin_shared/.idea/workspace.xml.tmpl", "templates/plugin_shared/.metadata.tmpl", "templates/plugin_shared/analysis_options.yaml.tmpl", "templates/plugin_shared/android.tmpl/.gitignore", "templates/plugin_shared/android.tmpl/settings.gradle.tmpl", "templates/plugin_shared/android.tmpl/src/main/AndroidManifest.xml.tmpl", "templates/plugin_shared/CHANGELOG.md.tmpl", "templates/plugin_shared/LICENSE.tmpl", "templates/plugin_shared/macos.tmpl/projectName.podspec.tmpl", "templates/plugin_shared/projectName.iml.tmpl", "templates/plugin_shared/pubspec.yaml.tmpl", "templates/plugin_shared/windows.tmpl/.gitignore", "templates/plugin_cocoapods/ios-objc.tmpl/Classes/pluginClass.h.tmpl", "templates/plugin_cocoapods/ios-objc.tmpl/Classes/pluginClass.m.tmpl", "templates/plugin_cocoapods/ios-swift.tmpl/Classes/pluginClass.swift.tmpl", "templates/plugin_cocoapods/ios.tmpl/Assets/.gitkeep", "templates/plugin_cocoapods/ios.tmpl/Resources/PrivacyInfo.xcprivacy", "templates/plugin_cocoapods/macos.tmpl/Classes/pluginClass.swift.tmpl", "templates/plugin_swift_package_manager/ios-objc.tmpl/projectName.tmpl/Sources/projectName.tmpl/include/projectName.tmpl/pluginClass.h.tmpl", "templates/plugin_swift_package_manager/ios-objc.tmpl/projectName.tmpl/Sources/projectName.tmpl/pluginClass.m.tmpl", "templates/plugin_swift_package_manager/ios-objc.tmpl/projectName.tmpl/Package.swift.tmpl", "templates/plugin_swift_package_manager/ios-swift.tmpl/projectName.tmpl/Sources/projectName.tmpl/pluginClass.swift.tmpl", "templates/plugin_swift_package_manager/ios-swift.tmpl/projectName.tmpl/Package.swift.tmpl", "templates/plugin_swift_package_manager/ios.tmpl/projectName.tmpl/Sources/projectName.tmpl/PrivacyInfo.xcprivacy", "templates/plugin_swift_package_manager/macos.tmpl/projectName.tmpl/Sources/projectName.tmpl/Resources/.gitkeep", "templates/plugin_swift_package_manager/macos.tmpl/projectName.tmpl/Sources/projectName.tmpl/pluginClass.swift.tmpl", "templates/plugin_swift_package_manager/macos.tmpl/projectName.tmpl/Package.swift.tmpl", "templates/skeleton/assets/images/2.0x/flutter_logo.png.img.tmpl", "templates/skeleton/assets/images/3.0x/flutter_logo.png.img.tmpl", "templates/skeleton/assets/images/flutter_logo.png.img.tmpl", "templates/skeleton/l10n.yaml.tmpl", "templates/skeleton/lib/main.dart.tmpl", "templates/skeleton/lib/src/app.dart.tmpl", "templates/skeleton/lib/src/sample_feature/sample_item.dart.tmpl", "templates/skeleton/lib/src/sample_feature/sample_item_details_view.dart.tmpl", "templates/skeleton/lib/src/sample_feature/sample_item_list_view.dart.tmpl", "templates/skeleton/lib/src/localization/app_en.arb.tmpl", "templates/skeleton/lib/src/settings/settings_controller.dart.tmpl", "templates/skeleton/lib/src/settings/settings_service.dart.tmpl", "templates/skeleton/lib/src/settings/settings_view.dart.tmpl", "templates/skeleton/pubspec.yaml.tmpl", "templates/skeleton/README.md.tmpl", "templates/skeleton/test/implementation_test.dart.test.tmpl", "templates/skeleton/test/unit_test.dart.tmpl", "templates/skeleton/test/widget_test.dart.tmpl", "templates/xcode/ios/custom_application_bundle/Runner.xcworkspace.tmpl/contents.xcworkspacedata", "templates/xcode/ios/custom_application_bundle/Runner.xcworkspace.tmpl/xcshareddata/IDEWorkspaceChecks.plist", "templates/xcode/ios/custom_application_bundle/Runner.xcworkspace.tmpl/xcshareddata/WorkspaceSettings.xcsettings", "templates/xcode/ios/custom_application_bundle/Runner.xcodeproj.tmpl/project.pbxproj", "templates/xcode/ios/custom_application_bundle/Runner.xcodeproj.tmpl/project.xcworkspace/contents.xcworkspacedata", "templates/xcode/ios/custom_application_bundle/Runner.xcodeproj.tmpl/project.xcworkspace/xcshareddata/IDEWorkspaceChecks.plist", "templates/xcode/ios/custom_application_bundle/Runner.xcodeproj.tmpl/project.xcworkspace/xcshareddata/WorkspaceSettings.xcsettings", "templates/xcode/ios/custom_application_bundle/Runner.xcodeproj.tmpl/xcshareddata/xcschemes/Runner.xcscheme.tmpl"]}